# ===================================
# Quota App Backend Configuration
# ===================================

# Database Configuration for quota.app
spring.datasource.url=******************************************************************************************************
spring.datasource.username=admindatabasequotaapp
spring.datasource.password=zNAj8TWuTh6:mfA
spring.datasource.driver-class-name=org.postgresql.Driver

# DMT Database Configuration
dmt.datasource.url=******************************************************************************************************
dmt.datasource.username=admindatabasequotaapp
dmt.datasource.password=zNAj8TWuTh6:mfA
dmt.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mySecretKey
jwt.expiration=86400000

# CORS Configuration
cors.allowed.origins=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Spring Session Configuration
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.session.timeout=30m

# Session Cookie Configuration
server.servlet.session.cookie.name=QUOTA_SESSION
server.servlet.session.cookie.max-age=1800
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.http-only=true
# Set session cookie same-site attribute
server.servlet.session.cookie.same-site=lax

# Twilio Configuration for SMS notifications (Mobile App)
twilio.account.sid=${TWILIO_ACCOUNT_SID:your-twilio-account-sid}
twilio.auth.token=${TWILIO_AUTH_TOKEN:your-twilio-auth-token}
twilio.phone.number=${TWILIO_PHONE_NUMBER:your-twilio-phone-number}
twilio.enabled=${TWILIO_ENABLED:true}

# Logging Configuration
spring.main.banner-mode=off
logging.level.root=WARN
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.cors=TRACE
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.session=DEBUG
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql=WARN
logging.level.com.quotaapp.backend=DEBUG
logging.level.com.quotaapp.backend.filter=TRACE
